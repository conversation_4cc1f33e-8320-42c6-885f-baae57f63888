package com.cdkit.modules.cm.domain.budget.service;

import java.util.List;

/**
 * 季度预算详情查询服务
 * 负责查询季度预算的各种详情数据
 * 
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface QuarterlyBudgetDetailQueryService {

    /**
     * 根据季度预算ID查询采办包明细
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 采办包明细列表
     */
    List<ProcPkgDetailInfo> queryProcPkgDetailByMainId(String quarterlyBudgetId);

    /**
     * 根据季度预算ID查询原材料明细
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 原材料明细列表
     */
    List<MaterialDetailInfo> queryMaterialDetailByMainId(String quarterlyBudgetId);

    /**
     * 根据季度预算ID查询预算科目明细直接成本
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 预算科目明细直接成本列表
     */
    List<SubjectDirectCostInfo> querySubjectDirectCostByMainId(String quarterlyBudgetId);

    /**
     * 根据季度预算ID查询本中心间接成本
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 本中心间接成本列表
     */
    List<CenterIndirectCostInfo> queryCenterIndirectCostByMainId(String quarterlyBudgetId);

    /**
     * 根据季度预算ID查询综合管理间接成本
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 综合管理间接成本列表
     */
    List<ComprehensiveIndirectCostInfo> queryCompMageIndirectCostByMainId(String quarterlyBudgetId);

    /**
     * 根据季度预算ID查询非经营中心间接成本
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 非经营中心间接成本列表
     */
    List<NonOperatingIndirectCostInfo> queryNonOptCenterIndirectCostByMainId(String quarterlyBudgetId);

    /**
     * 根据季度预算ID查询收入明细
     *
     * @param quarterlyBudgetId 季度预算ID
     * @return 收入明细列表
     */
    List<RevenueDetailInfo> queryRevenueDetailByMainId(String quarterlyBudgetId);

    // ==================== 内部类定义 ====================

    /**
     * 采办包明细信息
     */
    class ProcPkgDetailInfo {
        private String id;
        private String quarterlyBudgetId;
        private String directCost;
        private String budgetSubjectCode;
        private String budgetSubjectName;
        private java.math.BigDecimal amount;
        private java.util.Date createTime;
        private String createBy;
        private java.util.Date updateTime;
        private String updateBy;
        private Integer tenantId;
        private Integer delFlag;
        private String sysOrgCode;

        // getter和setter方法
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getQuarterlyBudgetId() { return quarterlyBudgetId; }
        public void setQuarterlyBudgetId(String quarterlyBudgetId) { this.quarterlyBudgetId = quarterlyBudgetId; }
        public String getDirectCost() { return directCost; }
        public void setDirectCost(String directCost) { this.directCost = directCost; }
        public String getBudgetSubjectCode() { return budgetSubjectCode; }
        public void setBudgetSubjectCode(String budgetSubjectCode) { this.budgetSubjectCode = budgetSubjectCode; }
        public String getBudgetSubjectName() { return budgetSubjectName; }
        public void setBudgetSubjectName(String budgetSubjectName) { this.budgetSubjectName = budgetSubjectName; }
        public java.math.BigDecimal getAmount() { return amount; }
        public void setAmount(java.math.BigDecimal amount) { this.amount = amount; }
        public java.util.Date getCreateTime() { return createTime; }
        public void setCreateTime(java.util.Date createTime) { this.createTime = createTime; }
        public String getCreateBy() { return createBy; }
        public void setCreateBy(String createBy) { this.createBy = createBy; }
        public java.util.Date getUpdateTime() { return updateTime; }
        public void setUpdateTime(java.util.Date updateTime) { this.updateTime = updateTime; }
        public String getUpdateBy() { return updateBy; }
        public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }
        public Integer getTenantId() { return tenantId; }
        public void setTenantId(Integer tenantId) { this.tenantId = tenantId; }
        public Integer getDelFlag() { return delFlag; }
        public void setDelFlag(Integer delFlag) { this.delFlag = delFlag; }
        public String getSysOrgCode() { return sysOrgCode; }
        public void setSysOrgCode(String sysOrgCode) { this.sysOrgCode = sysOrgCode; }
    }

    /**
     * 原材料明细信息
     */
    class MaterialDetailInfo {
        private String id;
        private String quarterlyBudgetId;
        private String materialCode;
        private String materialName;
        private java.math.BigDecimal usageAmount;
        private String unit;
        private java.math.BigDecimal unitPriceExcludingTax;
        private java.math.BigDecimal totalPriceExcludingTax;
        private String compilationBasis;
        private String remark;
        private java.util.Date createTime;
        private String createBy;
        private java.util.Date updateTime;
        private String updateBy;
        private Integer tenantId;
        private Integer delFlag;
        private String sysOrgCode;

        // getter和setter方法
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getQuarterlyBudgetId() { return quarterlyBudgetId; }
        public void setQuarterlyBudgetId(String quarterlyBudgetId) { this.quarterlyBudgetId = quarterlyBudgetId; }
        public String getMaterialCode() { return materialCode; }
        public void setMaterialCode(String materialCode) { this.materialCode = materialCode; }
        public String getMaterialName() { return materialName; }
        public void setMaterialName(String materialName) { this.materialName = materialName; }
        public java.math.BigDecimal getUsageAmount() { return usageAmount; }
        public void setUsageAmount(java.math.BigDecimal usageAmount) { this.usageAmount = usageAmount; }
        public String getUnit() { return unit; }
        public void setUnit(String unit) { this.unit = unit; }
        public java.math.BigDecimal getUnitPriceExcludingTax() { return unitPriceExcludingTax; }
        public void setUnitPriceExcludingTax(java.math.BigDecimal unitPriceExcludingTax) { this.unitPriceExcludingTax = unitPriceExcludingTax; }
        public java.math.BigDecimal getTotalPriceExcludingTax() { return totalPriceExcludingTax; }
        public void setTotalPriceExcludingTax(java.math.BigDecimal totalPriceExcludingTax) { this.totalPriceExcludingTax = totalPriceExcludingTax; }
        public String getCompilationBasis() { return compilationBasis; }
        public void setCompilationBasis(String compilationBasis) { this.compilationBasis = compilationBasis; }
        public String getRemark() { return remark; }
        public void setRemark(String remark) { this.remark = remark; }
        public java.util.Date getCreateTime() { return createTime; }
        public void setCreateTime(java.util.Date createTime) { this.createTime = createTime; }
        public String getCreateBy() { return createBy; }
        public void setCreateBy(String createBy) { this.createBy = createBy; }
        public java.util.Date getUpdateTime() { return updateTime; }
        public void setUpdateTime(java.util.Date updateTime) { this.updateTime = updateTime; }
        public String getUpdateBy() { return updateBy; }
        public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }
        public Integer getTenantId() { return tenantId; }
        public void setTenantId(Integer tenantId) { this.tenantId = tenantId; }
        public Integer getDelFlag() { return delFlag; }
        public void setDelFlag(Integer delFlag) { this.delFlag = delFlag; }
        public String getSysOrgCode() { return sysOrgCode; }
        public void setSysOrgCode(String sysOrgCode) { this.sysOrgCode = sysOrgCode; }
    }

    /**
     * 收入明细信息
     */
    class RevenueDetailInfo {
        private String id;
        private String quarterlyBudgetId;
        private String revenueSubjectCode;
        private String revenueSubjectName;
        private java.math.BigDecimal revenueBudgetAmount;
        private java.util.Date createTime;
        private String createBy;
        private java.util.Date updateTime;
        private String updateBy;
        private Integer tenantId;
        private Integer delFlag;
        private String sysOrgCode;

        // getter和setter方法
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getQuarterlyBudgetId() { return quarterlyBudgetId; }
        public void setQuarterlyBudgetId(String quarterlyBudgetId) { this.quarterlyBudgetId = quarterlyBudgetId; }
        public String getRevenueSubjectCode() { return revenueSubjectCode; }
        public void setRevenueSubjectCode(String revenueSubjectCode) { this.revenueSubjectCode = revenueSubjectCode; }
        public String getRevenueSubjectName() { return revenueSubjectName; }
        public void setRevenueSubjectName(String revenueSubjectName) { this.revenueSubjectName = revenueSubjectName; }
        public java.math.BigDecimal getRevenueBudgetAmount() { return revenueBudgetAmount; }
        public void setRevenueBudgetAmount(java.math.BigDecimal revenueBudgetAmount) { this.revenueBudgetAmount = revenueBudgetAmount; }
        public java.util.Date getCreateTime() { return createTime; }
        public void setCreateTime(java.util.Date createTime) { this.createTime = createTime; }
        public String getCreateBy() { return createBy; }
        public void setCreateBy(String createBy) { this.createBy = createBy; }
        public java.util.Date getUpdateTime() { return updateTime; }
        public void setUpdateTime(java.util.Date updateTime) { this.updateTime = updateTime; }
        public String getUpdateBy() { return updateBy; }
        public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }
        public Integer getTenantId() { return tenantId; }
        public void setTenantId(Integer tenantId) { this.tenantId = tenantId; }
        public Integer getDelFlag() { return delFlag; }
        public void setDelFlag(Integer delFlag) { this.delFlag = delFlag; }
        public String getSysOrgCode() { return sysOrgCode; }
        public void setSysOrgCode(String sysOrgCode) { this.sysOrgCode = sysOrgCode; }
    }
}
